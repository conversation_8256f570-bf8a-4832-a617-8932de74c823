#!/usr/bin/env python3
"""
Interactive Feedback Script
Based on interactive-feedback-mcp by <PERSON><PERSON><PERSON>
Simplified version for direct LLM integration
"""

import os
import sys
import json
import argparse
import subprocess
import tempfile
import threading
import time
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import psutil
except ImportError:
    psutil = None

def get_terminal_size():
    """Get terminal size or default to 80x24"""
    try:
        import shutil
        columns, rows = shutil.get_terminal_size()
        return columns, rows
    except:
        return 80, 24

def print_header(title: str, char: str = "="):
    """Print a header with dynamic width"""
    columns, _ = get_terminal_size()
    print(char * columns)
    print(title)
    print(char * columns)

def interactive_feedback(project_directory: str = None, summary: str = None) -> Dict[str, Any]:
    """Collect interactive feedback from the user"""
    project_directory = project_directory or os.getcwd()
    
    # Clear screen for better visibility
    os.system('cls' if os.name == 'nt' else 'clear')
    print("\n" * 3)  # Add some spacing
    
    print_header("INTERACTIVE FEEDBACK REQUEST")
    print(f"Working directory: {project_directory}\n")

    if summary:
        print("Summary:", summary)
        print()

    print_header("Please provide your feedback:", char="-")
    print("(Press Enter twice to submit, or Ctrl+C to cancel)")
    print("-" * get_terminal_size()[0])

    # Collect multi-line input
    lines = []
    try:
        while True:
            line = input()
            if not line and (not lines or not lines[-1]):
                break
            lines.append(line)
    except KeyboardInterrupt:
        print("\nFeedback collection cancelled.")
        return {"interactive_feedback": None, "cancelled": True}

    feedback = "\n".join(lines)
    print(f"\nFeedback collected: {len(feedback)} characters\n")

    print_header("FEEDBACK SUMMARY")
    print("User feedback:")
    print(feedback)
    print("=" * get_terminal_size()[0])
    print()

    return {
        "interactive_feedback": feedback,
        "cancelled": False,
        "project_directory": project_directory,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }

def main():
    parser = argparse.ArgumentParser(description="Interactive Feedback Collection")
    parser.add_argument("--summary", help="Optional summary or context for the feedback request", default=None)
    parser.add_argument("--dir", help="Project directory (defaults to current directory)", default=None)
    
    args = parser.parse_args()
    result = interactive_feedback(args.dir, args.summary)
    
    if not result["cancelled"]:
        # Save feedback to file
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        feedback_file = Path(result["project_directory"]) / f"feedback_{timestamp}.json"
        with open(feedback_file, "w") as f:
            json.dump(result, f, indent=2)

if __name__ == "__main__":
    main() 